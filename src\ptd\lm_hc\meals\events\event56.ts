import { Player, Vector3, world, BlockPermutation, ExplosionOptions, system } from '@minecraft/server';

// Base property name for storing the fake diamond block coordinates
const FAKE_DIAMOND_BLOCKS_PROPERTY_BASE = 'ptd_lmhc:fake_diamond_blocks';

// Interface for cube data
interface DiamondCubeData {
  id: string;
  coordinates: Vector3[];
  timestamp: number;
}

/**
 * Gets the dimension-specific property name for storing fake diamond blocks
 *
 * @param dimensionId The dimension ID
 * @returns The dimension-specific property name
 */
function getDimensionPropertyName(dimensionId: string): string {
  // Sanitize the dimension ID to create a valid property name
  const sanitizedDimension = dimensionId.replace('minecraft:', '');
  return `${FAKE_DIAMOND_BLOCKS_PROPERTY_BASE}_${sanitizedDimension}`;
}

/**
 * Event 56: Fake O' Diamonds
 * Spawns 3x3x3 Fake Diamond Blocks that explode when broken
 *
 * @param player The player who triggered the event
 */
export function event56(player: Player): void {
  try {
    // Calculate position 5 blocks in the direction the player is facing
    const viewDirection = getPlayerViewDirection(player);

    // Calculate target position 5 blocks in the view direction
    const targetPos: Vector3 = {
      x: Math.floor(player.location.x + viewDirection.x * 5),
      y: Math.floor(player.location.y), // Will be adjusted to ground level
      z: Math.floor(player.location.z + viewDirection.z * 5)
    };

    // Find the ground level at the target position
    let groundY = targetPos.y;
    let foundGround = false;

    // Check up to 5 blocks down to find solid ground
    for (let yOffset = 0; yOffset >= -5; yOffset--) {
      const checkPos = {
        x: targetPos.x,
        y: targetPos.y + yOffset,
        z: targetPos.z
      };

      const block = player.dimension.getBlock(checkPos);

      if (block && block.type.id !== 'minecraft:air') {
        // Found solid ground, place cube starting 1 block above it (bottom layer of cube)
        groundY = checkPos.y + 1;
        foundGround = true;
        break;
      }
    }

    // If no ground found, check up to 5 blocks up (in case player is underground)
    if (!foundGround) {
      for (let yOffset = 1; yOffset <= 5; yOffset++) {
        const checkPos = {
          x: targetPos.x,
          y: targetPos.y + yOffset,
          z: targetPos.z
        };

        const block = player.dimension.getBlock(checkPos);

        if (block && block.type.id !== 'minecraft:air') {
          // Found solid ground, place cube starting 1 block above it (bottom layer of cube)
          groundY = checkPos.y + 1;
          foundGround = true;
          break;
        }
      }
    }

    // Final center position of the cube (centered above ground)
    // We want centerPos to be the middle of the 3x3x3 cube, so bottom layer is at groundY
    const centerPos: Vector3 = {
      x: targetPos.x,
      y: groundY + 1, // Center Y is 1 block above the bottom layer
      z: targetPos.z
    };

    // Array to store all diamond block coordinates for this cube
    const blockCoordinates: Vector3[] = [];

    // Spawn a 3x3x3 cube of diamond blocks centered at the position
    for (let x = -1; x <= 1; x++) {
      for (let y = -1; y <= 1; y++) {
        for (let z = -1; z <= 1; z++) {
          const blockPos: Vector3 = {
            x: centerPos.x + x,
            y: centerPos.y + y,
            z: centerPos.z + z
          };

          // Store the coordinates with rounded values for better matching
          blockCoordinates.push({
            x: Math.round(blockPos.x),
            y: Math.round(blockPos.y),
            z: Math.round(blockPos.z)
          });

          // Place diamond block
          const blockPerm = BlockPermutation.resolve('minecraft:diamond_block');
          player.dimension.getBlock(blockPos)?.setPermutation(blockPerm);
        }
      }
    }

    // Get the dimension ID
    const dimensionId = player.dimension.id;

    // Generate a unique ID for this cube (timestamp + location hash)
    const cubeId = `cube_${Date.now()}_${centerPos.x}_${centerPos.y}_${centerPos.z}`;

    // Store the block coordinates in the dimension-specific dynamic property
    storeCubeCoordinates(cubeId, blockCoordinates, dimensionId);

    // Play sound and particle effects
    player.dimension.playSound('random.levelup', centerPos, { volume: 1.0, pitch: 0.8 });
    player.dimension.spawnParticle('minecraft:villager_happy', centerPos);

    // Send message to player
    player.sendMessage('§b💎 Valuable diamond blocks appeared! But are they real? §b💎');
  } catch (error) {
    console.warn(`Error in event56: ${error}`);
  }
}

/**
 * Gets the view direction of a player as a normalized vector
 *
 * @param player The player to get the view direction for
 * @returns A normalized direction vector
 */
function getPlayerViewDirection(player: Player): Vector3 {
  // Get player's rotation in radians
  const rotation = player.getRotation();
  const yawRadians = (rotation.y + 90) * (Math.PI / 180);

  // Calculate direction vector (ignore Y component for now, just use XZ plane)
  const dirX = Math.cos(yawRadians);
  const dirZ = Math.sin(yawRadians);

  // Return normalized direction vector
  return {
    x: dirX,
    y: 0, // Ignore vertical component
    z: dirZ
  };
}

/**
 * Stores the fake diamond block coordinates in a dimension-specific dynamic property
 *
 * @param cubeId Unique ID for this cube
 * @param blockCoordinates Array of block coordinates
 * @param dimensionId The dimension ID where the cube is located
 */
function storeCubeCoordinates(cubeId: string, blockCoordinates: Vector3[], dimensionId: string): void {
  try {
    // Get the dimension-specific property name
    const propertyName = getDimensionPropertyName(dimensionId);

    // Create data structure for this cube
    const newCubeData: DiamondCubeData = {
      id: cubeId,
      coordinates: blockCoordinates,
      timestamp: Date.now()
    };

    // Get existing cubes data for this dimension
    let cubesData: DiamondCubeData[] = [];
    const existingDataJson = world.getDynamicProperty(propertyName);

    if (existingDataJson) {
      try {
        cubesData = JSON.parse(existingDataJson as string);
        if (!Array.isArray(cubesData)) {
          cubesData = []; // Reset if not an array (backward compatibility)
        }
      } catch (error) {
        console.warn(`Error parsing existing cube data: ${error}`);
        cubesData = [];
      }
    }

    // Add new cube data
    cubesData.push(newCubeData);

    // Store updated data in the dimension-specific property
    world.setDynamicProperty(propertyName, JSON.stringify(cubesData));
  } catch (error) {
    console.warn(`Error storing fake diamond block coordinates: ${error}`);
  }
}

/**
 * Process the breaking of a fake diamond block
 * Checks if the broken block is one of our fake diamond blocks and creates explosions
 *
 * @param player The player who broke the block
 * @param blockId The ID of the broken block
 * @param blockPos The position of the broken block
 * @returns True if processed as a fake diamond block, false otherwise
 */
export function handleFakeDiamondBlockBreak(player: Player, blockId: string, blockPos: Vector3): boolean {
  try {
    // If it's not a diamond block, ignore
    if (blockId !== 'minecraft:diamond_block') return false;

    // Get the dimension ID from the player
    const dimensionId = player.dimension.id;

    // Get the dimension-specific property name
    const propertyName = getDimensionPropertyName(dimensionId);

    // Get stored block coordinates for cubes in this dimension
    const allCubesJson = world.getDynamicProperty(propertyName);

    // Get stored block coordinates for all cubes
    if (!allCubesJson) return false;

    // Parse the JSON data
    let cubesData: DiamondCubeData[];
    try {
      cubesData = JSON.parse(allCubesJson as string);
      if (!Array.isArray(cubesData)) {
        return false;
      }
    } catch (error) {
      console.warn(`Error parsing cubes data: ${error}`);
      return false;
    }

    // Find which cube the broken block belongs to by checking if the coordinates match exactly
    let foundCubeIndex = -1;
    let foundCube: DiamondCubeData | null = null;

    // Round the block position for comparison
    const roundedPos: Vector3 = {
      x: Math.round(blockPos.x),
      y: Math.round(blockPos.y),
      z: Math.round(blockPos.z)
    };

    // Check each cube's coordinates
    for (let i = 0; i < cubesData.length; i++) {
      const cube = cubesData[i];

      if (!cube) continue; // Skip if cube is undefined

      // Check if any of the cube's coordinates match the broken block's coordinates
      const matchingCoord = cube.coordinates.find(
        (coord: Vector3) => coord.x === roundedPos.x && coord.y === roundedPos.y && coord.z === roundedPos.z
      );

      if (matchingCoord) {
        foundCubeIndex = i;
        foundCube = cube;
        break;
      }
    }

    // If no matching cube found, this is not a fake diamond block
    if (foundCube === null || foundCubeIndex === -1) {
      return false;
    }

    // We found a matching cube - this is a fake diamond block
    // First, set all blocks in the cube to air immediately
    setAllBlocksToAir(player, foundCube.coordinates);

    // Then trigger explosions for visual effect
    createSequentialExplosions(player, foundCube.coordinates);

    // Remove the cube from storage since it's been triggered
    cubesData.splice(foundCubeIndex, 1);

    // Update the dynamic property with the updated cube list
    world.setDynamicProperty(propertyName, JSON.stringify(cubesData));

    return true;
  } catch (error) {
    console.warn(`Error handling fake diamond block break: ${error}`);
    return false;
  }
}



/**
 * Sets all blocks in the cube to air immediately
 *
 * @param player The player who triggered the event
 * @param coordinates Array of coordinates to set to air
 */
function setAllBlocksToAir(player: Player, coordinates: Vector3[]): void {
  try {
    // Set all blocks to air immediately
    for (const coord of coordinates) {
      if (!coord) continue; // Skip if coord is undefined

      const block = player.dimension.getBlock(coord);
      if (block && block.type.id === 'minecraft:diamond_block') {
        block.setType('minecraft:air');
      }
    }

    // Play a sound effect to indicate the blocks disappeared
    player.dimension.playSound('random.pop', player.location, { volume: 1.0, pitch: 0.8 });
  } catch (error) {
    console.warn(`Error setting blocks to air: ${error}`);
  }
}

/**
 * Calculate distance between two positions
 *
 * @param pos1 First position
 * @param pos2 Second position
 * @returns Distance between positions
 */
function getDistance(pos1: Vector3, pos2: Vector3): number {
  const dx = pos2.x - pos1.x;
  const dy = pos2.y - pos1.y;
  const dz = pos2.z - pos1.z;

  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Creates explosions at each coordinate in sequence with a delay
 *
 * @param player The player who triggered the explosions
 * @param coordinates Array of coordinates to explode
 */
function createSequentialExplosions(player: Player, coordinates: Vector3[]): void {
  try {
    // Sort coordinates by distance from the first broken block (first to explode)
    // This creates a cascading explosion effect that spreads outward
    const centerPos = player.location;
    coordinates.sort((a, b) => getDistance(a, centerPos) - getDistance(b, centerPos));

    // Create explosions with a delay between each
    for (let i = 0; i < coordinates.length; i++) {
      const coord = coordinates[i];
      if (!coord) continue; // Skip if coord is undefined

      // Create delayed sequential explosion
      system.runTimeout(() => {
        createExplosionAt(player, coord);
      }, i * 3); // 3 tick delay between each explosion (0.15 seconds)
    }
  } catch (error) {
    console.warn(`Error creating sequential explosions: ${error}`);
  }
}

/**
 * Creates a single explosion at the specified coordinates
 *
 * @param player The player who triggered the explosion
 * @param coord Coordinates for the explosion
 */
function createExplosionAt(player: Player, coord: Vector3): void {
  try {
    // Create explosion options
    const explosionOptions: ExplosionOptions = {
      breaksBlocks: true,
      causesFire: false
    };

    // Create explosion
    player.dimension.createExplosion(coord, 2, explosionOptions);

    // Play explosion sound
    player.dimension.playSound('random.explode', coord);
  } catch (error) {
    console.warn(`Error creating explosion: ${error}`);
  }
}


