import { Player, Vector3, world, BlockPermutation, system, EntityItemComponent, EntityComponentTypes, ItemStack } from '@minecraft/server';

// Base property name for storing the event3 diamond block coordinates
const EVENT3_DIAMOND_BLOCKS_PROPERTY_BASE = 'ptd_lmhc:event3_diamond_blocks';

// Interface for cube data
interface DiamondCubeData {
  id: string;
  coordinates: Vector3[];
  timestamp: number;
}

/**
 * Gets the dimension-specific property name for storing event3 diamond blocks
 *
 * @param dimensionId The dimension ID
 * @returns The dimension-specific property name
 */
function getDimensionPropertyName(dimensionId: string): string {
  // Sanitize the dimension ID to create a valid property name
  const sanitizedDimension = dimensionId.replace('minecraft:', '');
  return `${EVENT3_DIAMOND_BLOCKS_PROPERTY_BASE}_${sanitizedDimension}`;
}

/**
 * Event 3: Bunch O' Diamonds
 * Spawns 3x3x3 Diamond Blocks that drop 1 diamond item each when broken
 *
 * @param player The player who triggered the event
 */
export function event3(player: Player): void {
  try {
    // Calculate position 5 blocks in the direction the player is facing
    const viewDirection = getPlayerViewDirection(player);

    // Calculate target position 5 blocks in the view direction
    const targetPos: Vector3 = {
      x: Math.floor(player.location.x + viewDirection.x * 5),
      y: Math.floor(player.location.y), // Will be adjusted to ground level
      z: Math.floor(player.location.z + viewDirection.z * 5)
    };

    // Find the ground level at the target position
    let groundY = targetPos.y;
    let foundGround = false;

    // Check up to 5 blocks down to find solid ground
    for (let yOffset = 0; yOffset >= -5; yOffset--) {
      const checkPos = {
        x: targetPos.x,
        y: targetPos.y + yOffset,
        z: targetPos.z
      };

      const block = player.dimension.getBlock(checkPos);

      if (block && block.type.id !== 'minecraft:air') {
        // Found solid ground, place cube starting 1 block above it (bottom layer of cube)
        groundY = checkPos.y + 1;
        foundGround = true;
        break;
      }
    }

    // If no ground found, check up to 5 blocks up (in case player is underground)
    if (!foundGround) {
      for (let yOffset = 1; yOffset <= 5; yOffset++) {
        const checkPos = {
          x: targetPos.x,
          y: targetPos.y + yOffset,
          z: targetPos.z
        };

        const block = player.dimension.getBlock(checkPos);

        if (block && block.type.id !== 'minecraft:air') {
          // Found solid ground, place cube starting 1 block above it (bottom layer of cube)
          groundY = checkPos.y + 1;
          foundGround = true;
          break;
        }
      }
    }

    // Final center position of the cube (centered above ground)
    // We want centerPos to be the middle of the 3x3x3 cube, so bottom layer is at groundY
    const centerPos: Vector3 = {
      x: targetPos.x,
      y: groundY + 1, // Center Y is 1 block above the bottom layer
      z: targetPos.z
    };

    // Array to store all diamond block coordinates for this cube
    const blockCoordinates: Vector3[] = [];

    // Spawn a 3x3x3 cube of diamond blocks centered at the position
    for (let x = -1; x <= 1; x++) {
      for (let y = -1; y <= 1; y++) {
        for (let z = -1; z <= 1; z++) {
          const blockPos: Vector3 = {
            x: centerPos.x + x,
            y: centerPos.y + y,
            z: centerPos.z + z
          };

          // Store the coordinates with rounded values for better matching
          blockCoordinates.push({
            x: Math.round(blockPos.x),
            y: Math.round(blockPos.y),
            z: Math.round(blockPos.z)
          });

          // Place diamond block
          const blockPerm = BlockPermutation.resolve('minecraft:diamond_block');
          player.dimension.getBlock(blockPos)?.setPermutation(blockPerm);
        }
      }
    }

    // Get the dimension ID
    const dimensionId = player.dimension.id;

    // Generate a unique ID for this cube (timestamp + location hash)
    const cubeId = `cube_${Date.now()}_${centerPos.x}_${centerPos.y}_${centerPos.z}`;

    // Store the block coordinates in the dimension-specific dynamic property
    storeCubeCoordinates(cubeId, blockCoordinates, dimensionId);

    // Play sound and particle effects
    player.dimension.playSound('random.levelup', centerPos, { volume: 1.0, pitch: 0.8 });
    player.dimension.spawnParticle('minecraft:villager_happy', centerPos);

  } catch (error) {
    console.warn(`Error in event3: ${error}`);
  }
}

/**
 * Gets the view direction of a player as a normalized vector
 *
 * @param player The player to get the view direction for
 * @returns A normalized direction vector
 */
function getPlayerViewDirection(player: Player): Vector3 {
  // Get player's rotation in radians
  const rotation = player.getRotation();
  const yawRadians = (rotation.y + 90) * (Math.PI / 180);

  // Calculate direction vector (ignore Y component for now, just use XZ plane)
  const dirX = Math.cos(yawRadians);
  const dirZ = Math.sin(yawRadians);

  // Return normalized direction vector
  return {
    x: dirX,
    y: 0, // Ignore vertical component
    z: dirZ
  };
}

/**
 * Stores the event3 diamond block coordinates in a dimension-specific dynamic property
 *
 * @param cubeId Unique ID for this cube
 * @param blockCoordinates Array of block coordinates
 * @param dimensionId The dimension ID where the cube is located
 */
function storeCubeCoordinates(cubeId: string, blockCoordinates: Vector3[], dimensionId: string): void {
  try {
    // Get the dimension-specific property name
    const propertyName = getDimensionPropertyName(dimensionId);

    // Create data structure for this cube
    const newCubeData: DiamondCubeData = {
      id: cubeId,
      coordinates: blockCoordinates,
      timestamp: Date.now()
    };

    // Get existing cubes data for this dimension
    let cubesData: DiamondCubeData[] = [];
    const existingDataJson = world.getDynamicProperty(propertyName);

    if (existingDataJson) {
      try {
        cubesData = JSON.parse(existingDataJson as string);
        if (!Array.isArray(cubesData)) {
          cubesData = []; // Reset if not an array (backward compatibility)
        }
      } catch (error) {
        console.warn(`Error parsing existing cube data: ${error}`);
        cubesData = [];
      }
    }

    // Add new cube data
    cubesData.push(newCubeData);

    // Store updated data in the dimension-specific property
    world.setDynamicProperty(propertyName, JSON.stringify(cubesData));
  } catch (error) {
    console.warn(`Error storing event3 diamond block coordinates: ${error}`);
  }
}

/**
 * Process the breaking of an event3 diamond block
 * Checks if the broken block is one of our event3 diamond blocks and removes only that specific block from tracking
 *
 * @param player The player who broke the block
 * @param blockId The ID of the broken block
 * @param blockPos The position of the broken block
 * @returns True if processed as an event3 diamond block, false otherwise
 */
export function handleEvent3DiamondBlockBreak(player: Player, blockId: string, blockPos: Vector3): boolean {
  try {
    // If it's not a diamond block, ignore
    if (blockId !== 'minecraft:diamond_block') return false;

    // Get the dimension ID from the player
    const dimensionId = player.dimension.id;

    // Get the dimension-specific property name
    const propertyName = getDimensionPropertyName(dimensionId);

    // Get stored block coordinates for cubes in this dimension
    const allCubesJson = world.getDynamicProperty(propertyName);

    // Get stored block coordinates for all cubes
    if (!allCubesJson) return false;

    // Parse the JSON data
    let cubesData: DiamondCubeData[];
    try {
      cubesData = JSON.parse(allCubesJson as string);
      if (!Array.isArray(cubesData)) {
        return false;
      }
    } catch (error) {
      console.warn(`Error parsing cubes data: ${error}`);
      return false;
    }

    // Find which cube the broken block belongs to by checking if the coordinates match exactly
    let foundCubeIndex = -1;
    let foundCube: DiamondCubeData | null = null;
    let brokenBlockCoordIndex = -1;

    // Round the block position for comparison
    const roundedPos: Vector3 = {
      x: Math.round(blockPos.x),
      y: Math.round(blockPos.y),
      z: Math.round(blockPos.z)
    };

    // Check each cube's coordinates
    for (let i = 0; i < cubesData.length; i++) {
      const cube = cubesData[i];

      if (!cube) continue; // Skip if cube is undefined

      // Check if any of the cube's coordinates match the broken block's coordinates
      const matchingCoordIndex = cube.coordinates.findIndex(
        (coord) => coord.x === roundedPos.x && coord.y === roundedPos.y && coord.z === roundedPos.z
      );

      if (matchingCoordIndex !== -1) {
        foundCubeIndex = i;
        foundCube = cube;
        brokenBlockCoordIndex = matchingCoordIndex;
        break;
      }
    }

    // If no matching cube found, this is not an event3 diamond block
    if (foundCube === null || foundCubeIndex === -1 || brokenBlockCoordIndex === -1) {
      return false;
    }

    // We found a matching cube - this is an event3 diamond block
    // Remove only the broken block from the cube's coordinate list
    foundCube.coordinates.splice(brokenBlockCoordIndex, 1);

    // If the cube has no more blocks, remove the entire cube
    if (foundCube.coordinates.length === 0) {
      cubesData.splice(foundCubeIndex, 1);
    }

    // Update the dynamic property with the updated cube list
    world.setDynamicProperty(propertyName, JSON.stringify(cubesData));

    return true;
  } catch (error) {
    console.warn(`Error handling event3 diamond block break: ${error}`);
    return false;
  }
}

/**
 * Handles diamond block item collection for event3 - drops 1 diamond for each broken event3 diamond block
 * Finds diamond block items and replaces them with a single diamond item
 *
 * @param player The player who broke the block
 * @param blockPos The position where the block was broken
 */
export function handleEvent3DiamondItemCollection(player: Player, blockPos: Vector3): void {
  try {
    // Wait a short time for items to drop
    system.runTimeout(() => {
      // Find all diamond block items within 4 blocks of the broken block position
      const diamondBlockItems = player.dimension.getEntities({
        type: 'minecraft:item',
        location: blockPos,
        maxDistance: 4
      });

      let diamondBlockCount = 0;
      const itemsToRemove: any[] = [];

      // Check each item entity to see if it's a diamond block
      for (const itemEntity of diamondBlockItems) {
        const itemComponent = itemEntity.getComponent(EntityComponentTypes.Item) as EntityItemComponent;

        if (itemComponent && itemComponent.itemStack.type.id === 'minecraft:diamond_block') {
          diamondBlockCount += itemComponent.itemStack.amount;
          itemsToRemove.push(itemEntity);
        }
      }

      // If we found diamond block items, remove all and spawn exactly 1 diamond
      if (diamondBlockCount > 0) {
        // Remove all diamond block item entities
        for (const itemEntity of itemsToRemove) {
          itemEntity.kill();
        }

        // Spawn exactly 1 diamond for this broken event3 diamond block
        player.dimension.spawnItem(
          new ItemStack('minecraft:diamond', 1),
          {
            x: blockPos.x + 0.5,
            y: blockPos.y + 0.5,
            z: blockPos.z + 0.5
          }
        );

        // Notify player about the transformation
        player.sendMessage(`§e💎 The diamond block turned into a diamond! §e💎`);
      }
    }, 5); // 5 tick delay to allow items to drop
  } catch (error) {
    console.warn(`Error handling event3 diamond item collection: ${error}`);
  }
}
