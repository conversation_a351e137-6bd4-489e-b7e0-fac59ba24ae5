import { BlockTypes } from '@minecraft/server';
/**
 * Event 3. Bunch O' Diamonds: Spawns a 3x3x3 cube of diamond blocks centered on the player's position.
 * Creates a total of 27 diamond blocks arranged in a perfect cube.
 *
 * @param player - The player around whom the diamond blocks will spawn
 * @throws Will log a warning if the block placement fails
 */
export function event3(player) {
    try {
        const playerPos = player.getHeadLocation();
        // Offset starting position to center the cube around the player
        const startPos = {
            x: Math.floor(playerPos.x) - 1,
            y: Math.floor(playerPos.y),
            z: Math.floor(playerPos.z) - 1
        };
        // Get diamond block type
        const diamondBlockType = BlockTypes.get('minecraft:diamond_block');
        if (!diamondBlockType) {
            throw new Error('Failed to get diamond block type');
        }
        // Create a 3x3x3 cube of diamond blocks
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                for (let z = 0; z < 3; z++) {
                    const blockPos = {
                        x: startPos.x + x,
                        y: startPos.y + y,
                        z: startPos.z + z
                    };
                    const block = player.dimension.getBlock(blockPos);
                    if (block) {
                        block.setType(diamondBlockType);
                    }
                }
            }
        }
    }
    catch (error) {
        console.warn(`Failed to spawn diamond block cube: ${error}`);
    }
}
