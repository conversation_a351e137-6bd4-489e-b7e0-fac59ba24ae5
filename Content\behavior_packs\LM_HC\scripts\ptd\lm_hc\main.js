import { Player, system, world } from '@minecraft/server';
import { meals, onEatMeal } from './meals/events';
import { registerFirstSpawnGuidebook } from "./player/index";
import { initializeGuidebookSystem, guidebookComponent, updatePlayerGuidebookPageProperty, processGuidebook } from './meals/guidebook';
import { event1 } from './meals/events/event1';
import { event2 } from './meals/events/event2';
import { event3 } from './meals/events/event3';
import { event4 } from './meals/events/event4';
import { event5 } from './meals/events/event5';
import { event6 } from './meals/events/event6';
import { event7 } from './meals/events/event7';
import { event8 } from './meals/events/event8';
import { event9 } from './meals/events/event9';
import { event10 } from './meals/events/event10';
import { event11 } from './meals/events/event11';
import { event12 } from './meals/events/event12';
import { event13 } from './meals/events/event13';
import { event14 } from './meals/events/event14';
import { event15 } from './meals/events/event15';
import { event16 } from './meals/events/event16';
import { event17 } from './meals/events/event17';
import { event18 } from './meals/events/event18';
import { event19 } from './meals/events/event19';
import { event20 } from './meals/events/event20';
import { event21 } from './meals/events/event21';
import { event22 } from './meals/events/event22';
import { event23 } from './meals/events/event23';
import { event24 } from './meals/events/event24';
import { event25 } from './meals/events/event25';
import { event26 } from './meals/events/event26';
import { event27 } from './meals/events/event27';
import { event28 } from './meals/events/event28';
import { event29 } from './meals/events/event29';
import { event30 } from './meals/events/event30';
import { event31 } from './meals/events/event31';
import { event32 } from './meals/events/event32';
import { event33 } from './meals/events/event33';
import { event34 } from './meals/events/event34';
import { event35 } from './meals/events/event35';
import { event36 } from './meals/events/event36';
import { event37 } from './meals/events/event37';
import { event38 } from './meals/events/event38';
import { event39 } from './meals/events/event39';
import { event40 } from './meals/events/event40';
import { event41 } from './meals/events/event41';
import { event42 } from './meals/events/event42';
import { event43, handleLuckyCowInteraction } from './meals/events/event43';
import { event44 } from './meals/events/event44';
import { event45 } from './meals/events/event45';
import { event46 } from './meals/events/event46';
import { event47 } from './meals/events/event47';
import { event48 } from './meals/events/event48';
import { event49 } from './meals/events/event49';
import { event50 } from './meals/events/event50';
import { event51 } from './meals/events/event51';
import { event52 } from './meals/events/event52';
import { event53 } from './meals/events/event53';
import { event54 } from './meals/events/event54';
import { event55 } from './meals/events/event55';
import { event56 } from './meals/events/event56';
import { event57 } from './meals/events/event57';
import { event58 } from './meals/events/event58';
import { event59 } from './meals/events/event59';
import { event60 } from './meals/events/event60';
import { event61 } from './meals/events/event61';
import { event62 } from './meals/events/event62';
import { event63 } from './meals/events/event63';
import { event64 } from './meals/events/event64';
import { event65 } from './meals/events/event65';
import { event66 } from './meals/events/event66';
import { event67 } from './meals/events/event67';
import { event68 } from './meals/events/event68';
import { event69 } from './meals/events/event69';
import { event70 } from './meals/events/event70';
import { event71 } from './meals/events/event71';
import { event72 } from './meals/events/event72';
import { event73, handleDivineEscapeDamage, DIVINE_ESCAPE_TAG } from './meals/events/event73';
import { event74 } from './meals/events/event74';
import { event75 } from './meals/events/event75';
import { destroyLuckyPot, LuckyPotInteract } from './meals/LuckyPot';
import { destroyLuckyFoodTrophy } from './meals/LuckyFoodTrophy';
import { setEntityToCardinalDirection } from './utilities/rotation';
import { updateAchievements } from './meals/achievement';
import { handleItemDropInWell } from './meals/events/event17';
import { handleLuckyElixirConsume } from './meals/events/event23';
import { minerHelmetMechanics } from './meals/events/event27';
import { checkCursedPickaxe } from './meals/events/event28';
import { flightMechanics } from './meals/events/event29';
import { handleUnluckyElixirConsume } from './meals/events/event44';
import { handleEvent3DiamondBlockBreak, handleEvent3DiamondItemCollection } from './meals/events/event3';
/**
 * @description Registers a custom item component for meals.
 * @param {WorldInitializeAfterEvent} data - The event data.
 * @returns {void}
 */
world.beforeEvents.worldInitialize.subscribe((data) => {
    // Initialize the guidebook system
    initializeGuidebookSystem();
    // Register meals component
    data.itemComponentRegistry.registerCustomComponent('ptd_lmhc:meals', {
        onConsume(ev) {
            const entity = ev.source;
            const itemTypeId = ev.itemStack.type.id;
            if (entity instanceof Player && meals.has(itemTypeId)) {
                onEatMeal(entity);
            }
            else if (entity instanceof Player && itemTypeId === 'ptd_lmhc:lucky_elixir') {
                // Both Lucky Elixir (event 23) and Unlucky Elixir (event 44) use the same item type
                // We differentiate them using a dynamic property
                const isUnluckyElixir = ev.itemStack.getDynamicProperty('ptd_lmhc:is_unlucky_elixir');
                if (isUnluckyElixir) {
                    // This is an unlucky elixir from event 44, apply negative effects
                    // (wither, nausea, poison, slowness)
                    handleUnluckyElixirConsume(entity);
                }
                else {
                    // This is a lucky elixir from event 23, apply positive effects
                    // (invisibility, speed, absorption)
                    handleLuckyElixirConsume(entity);
                }
            }
        }
    });
    // Register guidebook component
    data.itemComponentRegistry.registerCustomComponent('ptd_lmhc:guidebook', guidebookComponent);
    return;
});
/**
 * Starts the global interval for game mechanics
 * Handles multiple systems: miner helmet lighting and guidebook following
 */
system.runTimeout(() => {
    // Define dimensions once for reuse
    const dimensionIds = ['overworld', 'nether', 'the_end'];
    const dimensions = dimensionIds.map((id) => world.getDimension(id));
    system.runInterval(() => {
        try {
            // Iterate through each dimension
            dimensions.forEach((dimension) => {
                // Player mechanics
                const players = dimension.getPlayers();
                players.forEach((player) => {
                    if (player) {
                        minerHelmetMechanics(player);
                        checkCursedPickaxe(player);
                        flightMechanics(player);
                    }
                });
                // Guidebook system
                const guidebooks = dimension.getEntities({
                    type: 'ptd_lmhc:guidebook'
                });
                guidebooks.forEach((guidebook) => {
                    if (guidebook) {
                        processGuidebook(guidebook, dimension);
                    }
                });
            });
        }
        catch (error) {
            console.warn(`Error in global mechanics interval: ${error}`);
        }
    });
}, 100); // Delay to ensure the system is ready
/**
 * @description Handles player interaction with entities.
 * @param {BeforeEvent.PlayerInteractWithEntityEvent} ev - The event data.
 * @returns {void}
 */
world.beforeEvents.playerInteractWithEntity.subscribe((ev) => {
    const entity = ev.target;
    const player = ev.player;
    const item = ev.itemStack;
    const entityTypeId = entity.typeId;
    // Handle Lucky Cow interaction (mooshroom with Lucky Cow nameTag or empty nameTag)
    if (entityTypeId === 'minecraft:mooshroom' &&
        (entity.nameTag === '§6Lucky Cow§r' || entity.nameTag === '')) {
        // Handle the interaction with the Lucky Cow
        if (handleLuckyCowInteraction(player, entity)) {
            // If the interaction was handled, prevent the default behavior
            ev.cancel = true;
        }
        return;
    }
    if (entityTypeId === 'ptd_lmhc:lucky_pot') {
        let run = system.run(() => {
            LuckyPotInteract(entity, player, item);
            system.clearRun(run);
        });
    }
    else if (entityTypeId === 'ptd_lmhc:guidebook') {
        // Update player's stored page state when interacting with guidebook
        updatePlayerGuidebookPageProperty(player, entity);
    }
    return;
});
// Handle entity hit events
world.afterEvents.entityHitEntity.subscribe((data) => {
    const source = data.damagingEntity;
    const hitEntity = data.hitEntity;
    const entityTypeId = hitEntity.typeId;
    if (entityTypeId === 'ptd_lmhc:lucky_pot' && source instanceof Player) {
        destroyLuckyPot(hitEntity, source);
    }
    else if (entityTypeId === 'ptd_lmhc:lucky_food_trophy' && source instanceof Player) {
        destroyLuckyFoodTrophy(hitEntity, source);
    }
    return;
}, {
    entityTypes: [
        'ptd_lmhc:lucky_pot',
        'ptd_lmhc:lucky_food_trophy',
        'ptd_lmhc:guidebook',
        'minecraft:player'
    ]
});
// General player break block before event listener
world.beforeEvents.playerBreakBlock.subscribe((data) => {
    const block = data.block;
    const player = data.player;
    const blockLocation = block.center();
    const dimension = data.dimension;
    if (block.type.id === 'minecraft:diamond_block' && player.hasTag(`fake_diamond_block_${player.id}`)) {
        let run = system.run(() => {
            dimension.createExplosion(blockLocation, 4, {
                allowUnderwater: true,
                causesFire: false,
                breaksBlocks: true
            });
            player.removeTag(`fake_diamond_block_${player.id}`);
            system.clearRun(run);
        });
    }
    return;
}, { blockTypes: ['minecraft:diamond_block'] });
// Register block break event handler - Add this after the other event registrations
world.afterEvents.playerBreakBlock.subscribe((event) => {
    try {
        const { brokenBlockPermutation, player, block } = event;
        // Get the actual broken block coordinates from the event
        const blockPos = block.location;
        // Debug logging
        // console.warn(`Block broken at: ${JSON.stringify(blockPos)}, type: ${brokenBlockPermutation.type.id}`);
        // Handle potential event3 diamond block break
        const wasEvent3DiamondBlock = handleEvent3DiamondBlockBreak(player, brokenBlockPermutation.type.id, blockPos);
        // If it was an event3 diamond block, handle diamond item collection
        if (wasEvent3DiamondBlock) {
            handleEvent3DiamondItemCollection(player, blockPos);
        }
        // Note: event56 doesn't need item collection handling since it just explodes the blocks
    }
    catch (error) {
        console.warn(`Error in block break event handler: ${error}`);
    }
});
// For manual trigger of events via script events (typed in the chatbox in-game)
system.afterEvents.scriptEventReceive.subscribe((data) => {
    const id = data.id;
    const entity = data.sourceEntity;
    if (!(entity instanceof Player))
        return;
    if (id === 'ptd_lmhc:reset_events') {
        // Reset specific event dynamic properties for player
        for (let i = 1; i <= 75; i++) {
            updateAchievements(entity, i, false);
        }
        entity.sendMessage('Event properties have been reset.');
    }
    else if (id === 'ptd_lmhc:run_events') {
        // Reset specific event dynamic properties for player
        let currentEvent = 1;
        const eventInterval = system.runInterval(() => {
            if (currentEvent <= 75) {
                entity.runCommand(`scriptevent ptd_lmhc:event${currentEvent}`);
                currentEvent++;
            }
            else {
                entity.sendMessage('All events have been triggered.');
                system.clearRun(eventInterval);
            }
        }, 5);
        entity.sendMessage('Event properties have been reset.');
    }
    else {
        const eventNumber = parseInt(id.replace('ptd_lmhc:event', ''));
        if (isNaN(eventNumber) || eventNumber < 1 || eventNumber > 75)
            return;
        try {
            switch (eventNumber) {
                case 1:
                    event1(entity);
                    updateAchievements(entity, 1, true);
                    entity.sendMessage('Event §21§r: Treasure Trove');
                    break;
                case 2:
                    event2(entity);
                    updateAchievements(entity, 2, true);
                    entity.sendMessage('Event §22§r: TNT Rain');
                    break;
                case 3:
                    event3(entity);
                    updateAchievements(entity, 3, true);
                    entity.sendMessage("Event §23§r: Bunch O' Diamonds");
                    break;
                case 4:
                    event4(entity);
                    updateAchievements(entity, 4, true);
                    entity.sendMessage('Event §24§r: Anvil Shower');
                    break;
                case 5:
                    event5(entity);
                    updateAchievements(entity, 5, true);
                    entity.sendMessage("Event §25§r: Dragon's Hoard");
                    break;
                case 6:
                    event6(entity);
                    updateAchievements(entity, 6, true);
                    entity.sendMessage('Event §26§r: Supercharged Creeper');
                    break;
                case 7:
                    event7(entity);
                    updateAchievements(entity, 7, true);
                    entity.sendMessage('Event §27§r: Mystic Trader');
                    break;
                case 8:
                    event8(entity);
                    updateAchievements(entity, 8, true);
                    entity.sendMessage('Event §28§r: Wither Summon');
                    break;
                case 9:
                    event9(entity);
                    updateAchievements(entity, 9, true);
                    entity.sendMessage('Event §29§r: Totems of Undying');
                    break;
                case 10:
                    event10(entity);
                    updateAchievements(entity, 10, true);
                    entity.sendMessage('Event §210§r: Zombie Swarm');
                    break;
                case 11:
                    event11(entity);
                    updateAchievements(entity, 11, true);
                    entity.sendMessage('Event §211§r: Potions Galore');
                    break;
                case 12:
                    event12(entity);
                    updateAchievements(entity, 12, true);
                    entity.sendMessage('Event §212§r: Lava Pool');
                    break;
                case 13:
                    event13(entity);
                    updateAchievements(entity, 13, true);
                    entity.sendMessage('Event §213§r: Enchanted Arsenal');
                    break;
                case 14:
                    event14(entity);
                    updateAchievements(entity, 14, true);
                    entity.sendMessage('Event §214§r: Blindness Curse');
                    break;
                case 15:
                    event15(entity);
                    updateAchievements(entity, 15, true);
                    entity.sendMessage('Event §215§r: Wither Slayer');
                    break;
                case 16:
                    event16(entity);
                    updateAchievements(entity, 16, true);
                    entity.sendMessage('Event §216§r: Teleport to the End');
                    break;
                case 17:
                    event17(entity);
                    updateAchievements(entity, 17, true);
                    entity.sendMessage('Event §217§r: Wishing Well');
                    break;
                case 18:
                    event18(entity);
                    updateAchievements(entity, 18, true);
                    entity.sendMessage('Event §218§r: Bob the Zombie');
                    break;
                case 19:
                    event19(entity);
                    updateAchievements(entity, 19, true);
                    entity.sendMessage('Event §219§r: Pet Army');
                    break;
                case 20:
                    event20(entity);
                    updateAchievements(entity, 20, true);
                    entity.sendMessage('Event §220§r: Instant Hunger');
                    break;
                case 21:
                    event21(entity);
                    updateAchievements(entity, 21, true);
                    entity.sendMessage('Event §221§r: Super Speed Boots');
                    break;
                case 22:
                    event22(entity);
                    updateAchievements(entity, 22, true);
                    entity.sendMessage('Event §222§r: Shulker Hell');
                    break;
                case 23:
                    event23(entity);
                    updateAchievements(entity, 23, true);
                    entity.sendMessage('Event §223§r: Lucky Elixir');
                    break;
                case 24:
                    event24(entity);
                    updateAchievements(entity, 24, true);
                    entity.sendMessage('Event §224§r: Sky Drop');
                    break;
                case 25:
                    event25(entity);
                    updateAchievements(entity, 25, true);
                    entity.sendMessage('Event §225§r: Golem Guardian');
                    break;
                case 26:
                    event26(entity);
                    updateAchievements(entity, 26, true);
                    entity.sendMessage('Event §226§r: Exploding Blocks');
                    break;
                case 27:
                    event27(entity);
                    updateAchievements(entity, 27, true);
                    entity.sendMessage('Event §227§r: Mining Away');
                    break;
                case 28:
                    event28(entity);
                    updateAchievements(entity, 28, true);
                    entity.sendMessage('Event §228§r: Cursed Pickaxe');
                    break;
                case 29:
                    event29(entity);
                    updateAchievements(entity, 29, true);
                    entity.sendMessage('Event §229§r: Flying Mode');
                    break;
                case 30:
                    event30(entity);
                    updateAchievements(entity, 30, true);
                    entity.sendMessage('Event §230§r: Poisonous Feast');
                    break;
                case 31:
                    event31(entity);
                    updateAchievements(entity, 31, true);
                    entity.sendMessage('Event §231§r: Emerald Rain');
                    break;
                case 32:
                    event32(entity);
                    updateAchievements(entity, 32, true);
                    entity.sendMessage('Event §232§r: Piglin Invasion');
                    break;
                case 33:
                    event33(entity);
                    updateAchievements(entity, 33, true);
                    entity.sendMessage('Event §233§r: Diamond Meteor');
                    break;
                case 34:
                    event34(entity);
                    updateAchievements(entity, 34, true);
                    entity.sendMessage('Event §234§r: Obsidian Cage');
                    break;
                case 35:
                    event35(entity);
                    updateAchievements(entity, 35, true);
                    entity.sendMessage('Event §235§r: XP Bonanza');
                    break;
                case 36:
                    event36(entity);
                    updateAchievements(entity, 36, true);
                    entity.sendMessage("Event §236§r: Elder Guardian's Wrath");
                    break;
                case 37:
                    event37(entity);
                    updateAchievements(entity, 37, true);
                    entity.sendMessage('Event §237§r: OP Fishing Rod');
                    break;
                case 38:
                    entity.sendMessage('Event §238§r: Hotbar Vanish');
                    event38(entity);
                    updateAchievements(entity, 38, true);
                    break;
                case 39:
                    entity.sendMessage('Event §239§r: Piglin Bank');
                    event39(entity);
                    updateAchievements(entity, 39, true);
                    break;
                case 40:
                    entity.sendMessage('Event §240§r: Hostile Endermen');
                    event40(entity);
                    updateAchievements(entity, 40, true);
                    break;
                case 41:
                    event41(entity);
                    updateAchievements(entity, 41, true);
                    entity.sendMessage('Event §241§r: Instant House');
                    break;
                case 42:
                    event42(entity);
                    updateAchievements(entity, 42, true);
                    entity.sendMessage('Event §242§r: Mega Ravager');
                    break;
                case 43:
                    event43(entity);
                    updateAchievements(entity, 43, true);
                    entity.sendMessage('Event §243§r: Lucky Cow');
                    break;
                case 44:
                    event44(entity);
                    updateAchievements(entity, 44, true);
                    entity.sendMessage('Event §244§r: Unlucky Elixir');
                    break;
                case 45:
                    event45(entity);
                    updateAchievements(entity, 45, true);
                    entity.sendMessage('Event §245§r: Super Elytra');
                    break;
                case 46:
                    event46(entity);
                    updateAchievements(entity, 46, true);
                    entity.sendMessage('Event §246§r: Swarm of Bees');
                    break;
                case 47:
                    event47(entity);
                    updateAchievements(entity, 47, true);
                    entity.sendMessage('Event §247§r: Mob Convert');
                    break;
                case 48:
                    event48(entity);
                    updateAchievements(entity, 48, true);
                    entity.sendMessage('Event §248§r: Lightning Storm');
                    break;
                case 49:
                    event49(entity);
                    updateAchievements(entity, 49, true);
                    entity.sendMessage('Event §249§r: Lucky Horse');
                    break;
                case 50:
                    event50(entity);
                    updateAchievements(entity, 50, true);
                    entity.sendMessage('Event §250§r: Detonation');
                    break;
                case 51:
                    event51(entity);
                    updateAchievements(entity, 51, true);
                    entity.sendMessage('Event §251§r: Shulker Box Jackpot');
                    break;
                case 52:
                    event52(entity);
                    updateAchievements(entity, 52, true);
                    entity.sendMessage('Event §252§r: Silverfish Invasion');
                    break;
                case 53:
                    event53(entity);
                    updateAchievements(entity, 53, true);
                    entity.sendMessage('Event §253§r: Beacon of Hope');
                    break;
                case 54:
                    event54(entity);
                    updateAchievements(entity, 54, true);
                    entity.sendMessage('Event §254§r: Sudden Night');
                    break;
                case 55:
                    event55(entity);
                    updateAchievements(entity, 55, true);
                    entity.sendMessage('Event §255§r: Lucky Lava');
                    break;
                case 56:
                    event56(entity);
                    updateAchievements(entity, 56, true);
                    entity.sendMessage("Event §256§r: Fake O' Diamonds");
                    break;
                case 57:
                    event57(entity);
                    updateAchievements(entity, 57, true);
                    entity.sendMessage('Event §257§r: Mega Villager');
                    break;
                case 58:
                    event58(entity);
                    updateAchievements(entity, 58, true);
                    entity.sendMessage('Event §258§r: Item Vanish');
                    break;
                case 59:
                    event59(entity);
                    updateAchievements(entity, 59, true);
                    entity.sendMessage('Event §259§r: Mob Smite');
                    break;
                case 60:
                    event60(entity);
                    updateAchievements(entity, 60, true);
                    entity.sendMessage('Event §260§r: Mob Horde');
                    break;
                case 61:
                    event61(entity);
                    updateAchievements(entity, 61, true);
                    entity.sendMessage('Event §261§r: Sonic Boom');
                    break;
                case 62:
                    event62(entity);
                    updateAchievements(entity, 62, true);
                    entity.sendMessage('Event §262§r: Teleport Confusion');
                    break;
                case 63:
                    event63(entity);
                    updateAchievements(entity, 63, true);
                    entity.sendMessage('Event §263§r: Sheep Party');
                    break;
                case 64:
                    event64(entity);
                    updateAchievements(entity, 64, true);
                    entity.sendMessage("Event §264§r: Johnny's Here");
                    break;
                case 65:
                    event65(entity);
                    updateAchievements(entity, 65, true);
                    entity.sendMessage('Event §265§r: Cake Chamber');
                    break;
                case 66:
                    event66(entity);
                    updateAchievements(entity, 66, true);
                    entity.sendMessage('Event §266§r: Arrow Barrage');
                    break;
                case 67:
                    event67(entity);
                    updateAchievements(entity, 67, true);
                    entity.sendMessage('Event §267§r: Dinnerbone Party');
                    break;
                case 68:
                    event68(entity);
                    updateAchievements(entity, 68, true);
                    entity.sendMessage('Event §268§r: Cobweb Cage');
                    break;
                case 69:
                    event69(entity);
                    updateAchievements(entity, 69, true);
                    entity.sendMessage('Event §269§r: Ascension');
                    break;
                case 70:
                    event70(entity);
                    updateAchievements(entity, 70, true);
                    entity.sendMessage("Event §270§r: It's a bit Breezy");
                    break;
                case 71:
                    event71(entity);
                    updateAchievements(entity, 71, true);
                    entity.sendMessage('Event §271§r: Begone, Monsters!');
                    break;
                case 72:
                    event72(entity);
                    updateAchievements(entity, 72, true);
                    entity.sendMessage("Event §272§r: Wither's Breath");
                    break;
                case 73:
                    event73(entity);
                    updateAchievements(entity, 73, true);
                    entity.sendMessage('Event §273§r: Divine Escape');
                    break;
                case 74:
                    event74(entity);
                    updateAchievements(entity, 74, true);
                    entity.sendMessage('Event §274§r: Sluggish');
                    break;
                case 75:
                    event75(entity);
                    updateAchievements(entity, 75, true);
                    entity.sendMessage('Event §275§r: Treasure King');
                    break;
            }
        }
        catch (error) {
            console.warn(`Failed to execute event ${eventNumber}: ${error}`);
        }
    }
    return;
}, { namespaces: ['ptd_lmhc'] });
// Handle entityHurt events for Divine Escape (Event 73)
world.afterEvents.entityHurt.subscribe((event) => {
    const entity = event.hurtEntity;
    // Check if the hurt entity is a player with the divine_escape tag
    if (entity instanceof Player && entity.hasTag(DIVINE_ESCAPE_TAG)) {
        // Call the handler function from event73.ts
        handleDivineEscapeDamage(entity);
        return;
    }
});
// Register player first-spawn guidebook handler
registerFirstSpawnGuidebook();
// Add entity spawn event listener for spectral arrows and item drops
world.afterEvents.entitySpawn.subscribe((ev) => {
    const entity = ev.entity;
    if (!entity)
        return;
    try {
        const entityTypeId = entity.typeId;
        if (entityTypeId === 'ptd_lmhc:lucky_pot' || entityTypeId === 'ptd_lmhc:lucky_food_trophy') {
            setEntityToCardinalDirection(entity);
            // Add light block at entity location
            try {
                const blockPos = {
                    x: Math.floor(entity.location.x),
                    y: Math.floor(entity.location.y),
                    z: Math.floor(entity.location.z)
                };
                const block = entity.dimension.getBlock(blockPos);
                if (block && (block.type.id === 'minecraft:air' || block.type.id.startsWith('minecraft:light_block'))) {
                    block.setType('minecraft:light_block_12');
                }
            }
            catch (error) {
                console.warn(`Error placing light block: ${error}`);
            }
        }
        else if (entityTypeId === 'minecraft:item') {
            // Delegate item drop handling to the wishing well module
            handleItemDropInWell(entity);
        }
    }
    catch (error) {
        // Log errors for debugging
        console.warn(`Error in entitySpawn handler: ${error}`);
    }
    return;
});
