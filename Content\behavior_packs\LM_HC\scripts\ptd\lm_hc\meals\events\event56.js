import { world, BlockPermutation, system } from '@minecraft/server';
// Base property name for storing the explosion diamond block coordinates
const EXPLOSION_DIAMOND_BLOCKS_PROPERTY_BASE = 'ptd_lmhc:event56_explosion_blocks';
/**
 * Gets the dimension-specific property name for storing explosion diamond blocks
 *
 * @param dimensionId The dimension ID
 * @returns The dimension-specific property name
 */
function getDimensionPropertyName(dimensionId) {
    // Sanitize the dimension ID to create a valid property name
    const sanitizedDimension = dimensionId.replace('minecraft:', '');
    return `${EXPLOSION_DIAMOND_BLOCKS_PROPERTY_BASE}_${sanitizedDimension}`;
}
/**
 * Event 56: Explosive Diamond Cube
 * Spawns 3x3x3 Diamond Blocks that progressively explode when one is broken
 *
 * @param player The player who triggered the event
 */
export function event56(player) {
    try {
        // Calculate position 5 blocks in the direction the player is facing
        const viewDirection = getPlayerViewDirection(player);
        // Calculate target position 5 blocks in the view direction
        const targetPos = {
            x: Math.floor(player.location.x + viewDirection.x * 5),
            y: Math.floor(player.location.y), // Will be adjusted to ground level
            z: Math.floor(player.location.z + viewDirection.z * 5)
        };
        // Find the ground level at the target position
        let groundY = targetPos.y;
        let foundGround = false;
        // Check up to 5 blocks down to find solid ground
        for (let yOffset = 0; yOffset >= -5; yOffset--) {
            const checkPos = {
                x: targetPos.x,
                y: targetPos.y + yOffset,
                z: targetPos.z
            };
            const block = player.dimension.getBlock(checkPos);
            if (block && block.type.id !== 'minecraft:air') {
                // Found solid ground, place cube starting 1 block above it (bottom layer of cube)
                groundY = checkPos.y + 1;
                foundGround = true;
                break;
            }
        }
        // If no ground found, check up to 5 blocks up (in case player is underground)
        if (!foundGround) {
            for (let yOffset = 1; yOffset <= 5; yOffset++) {
                const checkPos = {
                    x: targetPos.x,
                    y: targetPos.y + yOffset,
                    z: targetPos.z
                };
                const block = player.dimension.getBlock(checkPos);
                if (block && block.type.id !== 'minecraft:air') {
                    // Found solid ground, place cube starting 1 block above it (bottom layer of cube)
                    groundY = checkPos.y + 1;
                    foundGround = true;
                    break;
                }
            }
        }
        // Final center position of the cube (centered above ground)
        // We want centerPos to be the middle of the 3x3x3 cube, so bottom layer is at groundY
        const centerPos = {
            x: targetPos.x,
            y: groundY + 1, // Center Y is 1 block above the bottom layer
            z: targetPos.z
        };
        // Array to store all diamond block coordinates for this cube
        const blockCoordinates = [];
        // Spawn a 3x3x3 cube of diamond blocks centered at the position
        for (let x = -1; x <= 1; x++) {
            for (let y = -1; y <= 1; y++) {
                for (let z = -1; z <= 1; z++) {
                    const blockPos = {
                        x: centerPos.x + x,
                        y: centerPos.y + y,
                        z: centerPos.z + z
                    };
                    // Store the coordinates with rounded values for better matching
                    blockCoordinates.push({
                        x: Math.round(blockPos.x),
                        y: Math.round(blockPos.y),
                        z: Math.round(blockPos.z)
                    });
                    // Place diamond block
                    const blockPerm = BlockPermutation.resolve('minecraft:diamond_block');
                    player.dimension.getBlock(blockPos)?.setPermutation(blockPerm);
                }
            }
        }
        // Get the dimension ID
        const dimensionId = player.dimension.id;
        // Generate a unique ID for this cube (timestamp + location hash)
        const cubeId = `cube_${Date.now()}_${centerPos.x}_${centerPos.y}_${centerPos.z}`;
        // Store the block coordinates in the dimension-specific dynamic property
        storeExplosionCubeCoordinates(cubeId, blockCoordinates, dimensionId);
        // Play sound and particle effects
        player.dimension.playSound('random.levelup', centerPos, { volume: 1.0, pitch: 0.8 });
        player.dimension.spawnParticle('minecraft:villager_happy', centerPos);
    }
    catch (error) {
        console.warn(`Error in event56: ${error}`);
    }
}
/**
 * Gets the view direction of a player as a normalized vector
 *
 * @param player The player to get the view direction for
 * @returns A normalized direction vector
 */
function getPlayerViewDirection(player) {
    // Get player's rotation in radians
    const rotation = player.getRotation();
    const yawRadians = (rotation.y + 90) * (Math.PI / 180);
    // Calculate direction vector (ignore Y component for now, just use XZ plane)
    const dirX = Math.cos(yawRadians);
    const dirZ = Math.sin(yawRadians);
    // Return normalized direction vector
    return {
        x: dirX,
        y: 0, // Ignore vertical component
        z: dirZ
    };
}
/**
 * Stores the explosion diamond block coordinates in a dimension-specific dynamic property
 *
 * @param cubeId Unique ID for this cube
 * @param blockCoordinates Array of block coordinates
 * @param dimensionId The dimension ID where the cube is located
 */
function storeExplosionCubeCoordinates(cubeId, blockCoordinates, dimensionId) {
    try {
        // Get the dimension-specific property name
        const propertyName = getDimensionPropertyName(dimensionId);
        // Create data structure for this cube
        const newCubeData = {
            id: cubeId,
            coordinates: blockCoordinates,
            timestamp: Date.now()
        };
        // Get existing cubes data for this dimension
        let cubesData = [];
        const existingDataJson = world.getDynamicProperty(propertyName);
        if (existingDataJson) {
            try {
                cubesData = JSON.parse(existingDataJson);
                if (!Array.isArray(cubesData)) {
                    cubesData = []; // Reset if not an array (backward compatibility)
                }
            }
            catch (error) {
                console.warn(`Error parsing existing cube data: ${error}`);
                cubesData = [];
            }
        }
        // Add new cube data
        cubesData.push(newCubeData);
        // Store updated data in the dimension-specific property
        world.setDynamicProperty(propertyName, JSON.stringify(cubesData));
    }
    catch (error) {
        console.warn(`Error storing explosion diamond block coordinates: ${error}`);
    }
}
/**
 * Process the breaking of an explosion diamond block
 * Checks if the broken block is one of our explosion diamond blocks and starts progressive explosions
 *
 * @param player The player who broke the block
 * @param blockId The ID of the broken block
 * @param blockPos The position of the broken block
 * @returns True if processed as an explosion diamond block, false otherwise
 */
export function handleExplosionDiamondBlockBreak(player, blockId, blockPos) {
    try {
        // If it's not a diamond block, ignore
        if (blockId !== 'minecraft:diamond_block')
            return false;
        // Get the dimension ID from the player
        const dimensionId = player.dimension.id;
        // Get the dimension-specific property name
        const propertyName = getDimensionPropertyName(dimensionId);
        // Get stored block coordinates for cubes in this dimension
        const allCubesJson = world.getDynamicProperty(propertyName);
        // Get stored block coordinates for all cubes
        if (!allCubesJson)
            return false;
        // Parse the JSON data
        let cubesData;
        try {
            cubesData = JSON.parse(allCubesJson);
            if (!Array.isArray(cubesData)) {
                return false;
            }
        }
        catch (error) {
            console.warn(`Error parsing cubes data: ${error}`);
            return false;
        }
        // Find which cube the broken block belongs to by checking if the coordinates match exactly
        let foundCubeIndex = -1;
        let foundCube = null;
        // Round the block position for comparison
        const roundedPos = {
            x: Math.round(blockPos.x),
            y: Math.round(blockPos.y),
            z: Math.round(blockPos.z)
        };
        // Check each cube's coordinates
        for (let i = 0; i < cubesData.length; i++) {
            const cube = cubesData[i];
            if (!cube)
                continue; // Skip if cube is undefined
            // Check if any of the cube's coordinates match the broken block's coordinates
            const matchingCoord = cube.coordinates.find((coord) => coord.x === roundedPos.x && coord.y === roundedPos.y && coord.z === roundedPos.z);
            if (matchingCoord) {
                foundCubeIndex = i;
                foundCube = cube;
                break;
            }
        }
        // If no matching cube found, this is not an explosion diamond block
        if (foundCube === null || foundCubeIndex === -1) {
            return false;
        }
        // We found a matching cube - this is an explosion diamond block
        // Start progressive explosions for all remaining blocks (except the one already broken)
        startProgressiveExplosions(player, foundCube.coordinates, blockPos);
        // Remove the cube from storage since it's been triggered
        cubesData.splice(foundCubeIndex, 1);
        // Update the dynamic property with the updated cube list
        world.setDynamicProperty(propertyName, JSON.stringify(cubesData));
        return true;
    }
    catch (error) {
        console.warn(`Error handling explosion diamond block break: ${error}`);
        return false;
    }
}
/**
 * Starts progressive explosions for all remaining diamond blocks in the cube (except the one already broken)
 * Each block explodes with a 5-tick delay between explosions
 *
 * @param player The player who broke the block
 * @param coordinates Array of coordinates in the cube
 * @param brokenBlockPos Position of the block that was already broken
 */
function startProgressiveExplosions(player, coordinates, brokenBlockPos) {
    try {
        // Round the broken block position for comparison
        const roundedBrokenPos = {
            x: Math.round(brokenBlockPos.x),
            y: Math.round(brokenBlockPos.y),
            z: Math.round(brokenBlockPos.z)
        };
        // Filter out the already broken block and get remaining blocks to explode
        const blocksToExplode = [];
        for (const coord of coordinates) {
            if (!coord)
                continue; // Skip if coord is undefined
            // Skip the block that was already broken
            if (coord.x === roundedBrokenPos.x && coord.y === roundedBrokenPos.y && coord.z === roundedBrokenPos.z) {
                continue;
            }
            // Check if the block is still a diamond block
            const block = player.dimension.getBlock(coord);
            if (block && block.type.id === 'minecraft:diamond_block') {
                blocksToExplode.push(coord);
            }
        }
        // Start progressive explosions with 5-tick delays
        let explosionIndex = 0;
        const explosionInterval = system.runInterval(() => {
            if (explosionIndex >= blocksToExplode.length) {
                // All blocks have been exploded, clear the interval
                system.clearRun(explosionInterval);
                return;
            }
            const blockPos = blocksToExplode[explosionIndex];
            if (blockPos) {
                // Create explosion at the block position
                player.dimension.createExplosion(blockPos, 2, {
                    allowUnderwater: true,
                    causesFire: false,
                    breaksBlocks: true
                });
                // Play explosion sound
                player.dimension.playSound('random.explode', blockPos, { volume: 1.0, pitch: 1.0 });
            }
            explosionIndex++;
        }, 5); // 5-tick delay between explosions
        // Play initial sound effect
        player.dimension.playSound('random.fuse', brokenBlockPos, { volume: 1.0, pitch: 0.8 });
    }
    catch (error) {
        console.warn(`Error starting progressive explosions: ${error}`);
    }
}
